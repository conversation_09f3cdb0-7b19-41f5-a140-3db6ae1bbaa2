package main

import (
	"bufio"
	"fmt"
	"log"
	"net"
	"os"
)

const serverAddr = "localhost:42069"

func main() {
	udpAddr, err := net.ResolveUDPAddr("udp", serverAddr)
	if err != nil {
		log.Fatal("could not resolve udp address")
	}
	conn, err := net.DialUDP("udp", nil, udpAddr)
	if err != nil {
		log.Fatal(err)
	}
	defer conn.Close()

	buff := bufio.NewReader(os.Stdin)

	for {
		fmt.Print(">")
		line, err := buff.ReadString('\n')

		if err != nil {
			log.Fatal(err)
		}

		conn.Write([]byte(line))
	}

}
