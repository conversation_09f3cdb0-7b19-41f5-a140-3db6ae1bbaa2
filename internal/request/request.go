package request

import (
	"bytes"
	"errors"
	"fmt"
	"io"
	"net"
	"regexp"
	"strings"
)

type request struct {
	requestLine requestLine
	headers     []string
	body        string
	state       string // request-line, headers, body, done
}
type requestLine struct {
	httpVersion   string
	requestTarget string
	method        string
}

const bufferSize = 8

func (r *request) parse(input io.Reader) error {
	buffer := make([]byte, bufferSize)
	crlf := []byte("\r\n")

	currentLine := make([]byte, 0)
	r.state = "request-line"

	for {
		n, err := input.Read(buffer)
		if err != nil {
			if errors.Is(err, io.EOF) {
				currentLine = append(currentLine, buffer[:n]...)
				break
			}
			return fmt.Errorf("error reading input")
		}

		parts := bytes.Split(buffer[:n], crlf)

		// crlf not found - continue reading the data..
		if len(parts) == 1 {
			currentLine = append(currentLine, parts[0]...)
			continue
		}

		// crlf found
		for i, part := range parts {
			currentLine = append(currentLine, part...)

			// the last element is either empty or not terminated with a crlf
			if i == len(parts)-1 {
				continue
			}

			if r.state == "body" {
				continue
			}
			switch r.state {
			case "request-line":
				fmt.Printf("debug request line %v \n", string(currentLine))
				err = r.ParseRequestLine(currentLine)
				if err != nil {
					return err
				}
				r.state = "headers"
			case "headers":
				// empty line = body marker
				if len(part) == 0 {
					r.state = "body"
				} else {
					r.headers = append(r.headers, string(currentLine))
					fmt.Printf("debug header %v len %v\n", string(currentLine), len(part))
				}
			}
			currentLine = make([]byte, 0)

		}
	}

	if r.state == "request-line" {
		return fmt.Errorf("could not parse supplied - no sections identified")
	}

	if r.state == "body" {
		r.body = string(currentLine)
	}
	return nil
}

// parse the request line into method/target/http version
func (r *request) ParseRequestLine(b []byte) error {

	line := string(b)

	if line == "" {
		return fmt.Errorf("empty string received")
	}

	lineComponents := strings.Split(line, " ")

	if len(lineComponents) != 3 {
		return fmt.Errorf("invalid string received - not enough parts: %v", len(lineComponents))
	}

	r.requestLine.method = lineComponents[0]
	r.requestLine.requestTarget = lineComponents[1]
	r.requestLine.httpVersion = lineComponents[2]

	// check method

	ok, err := regexp.Match(`[A-Z]+`, []byte(r.requestLine.method))
	if err != nil {
		return fmt.Errorf("could not parse method: %v", err)
	}

	if !ok {
		return fmt.Errorf("method must be all uppercase alpha")
	}
	// check version
	re := regexp.MustCompile(`((?i:HTTP))/(\d+\.\d+)`)

	if !re.Match([]byte(r.requestLine.httpVersion)) {
		return fmt.Errorf("httpVersion invalid: %v", r.requestLine.httpVersion)
	}

	matches := re.FindSubmatch([]byte(r.requestLine.httpVersion))

	if len(matches) != 3 {
		return fmt.Errorf("invalid HTTP version: %v", r.requestLine.httpVersion)
	}
	if strings.ToUpper(string(matches[1])) != "HTTP" {
		return fmt.Errorf("expected HTTP got: %v", r.requestLine.httpVersion)
	}

	if string(matches[2]) != "1.1" {
		return fmt.Errorf("only http 1.1 is supported, got: %v", r.requestLine.httpVersion)
	}
	return nil
}

func SendRequest(requestString string, serverAddr string) (int, error) {

	if requestString == "" {
		return 0, fmt.Errorf("empty request")
	}

	s := strings.NewReader(requestString)

	if serverAddr == "" {
		return 0, fmt.Errorf("serverAddr not specified")
	}

	b := make([]byte, s.Len())
	s.Read(b)

	// make a new connection to the server
	tcpAddr, err := net.ResolveTCPAddr("tcp", serverAddr)
	if err != nil {
		return 0, fmt.Errorf("could not resolve addr: %v", err)
	}

	conn, err := net.DialTCP("tcp", nil, tcpAddr)
	if err != nil {
		return 0, fmt.Errorf("could not open addr: %v", err)
	}
	defer conn.Close()

	i, err := conn.Write(b)
	if err != nil {
		if errors.Is(err, io.EOF) {
			return i, nil
		}
		return 0, fmt.Errorf("request error: %v", err)
	}
	return i, nil
}
