package request

import (
	"bytes"
	"errors"
	"fmt"
	"io"
	"net"
	"strings"
)

type request struct {
	requestLine requestLine
	headers     []string
	body        string
}
type requestLine struct {
	httpVersion   string
	requestTarget string
	method        string
}

const bufferSize = 4096 

// Simple parsing states
const (
	stateRequestLine = 0
	stateHeaders     = 1
	stateBody        = 2
)

func (r *request) parse(input io.Reader) error {
	// Reset request state
	r.headers = nil
	r.body = ""
	r.requestLine = requestLine{}

	buffer := make([]byte, bufferSize)
	var accumulated []byte
	state := stateRequestLine
	hasData := false

	for {
		n, err := input.Read(buffer)
		if err != nil && !errors.Is(err, io.EOF) {
			return fmt.Errorf("error reading input: %w", err)
		}

		if n > 0 {
			hasData = true
		}

		// Append new data to accumulated buffer
		accumulated = append(accumulated, buffer[:n]...)

		// Process complete lines
		for {
			lineEnd := bytes.Index(accumulated, []byte("\r\n"))
			if lineEnd == -1 {
				// No complete line found
				if errors.Is(err, io.EOF) {
					// Check for incomplete request
					if !hasData {
						return fmt.Errorf("empty request")
					}
					if len(accumulated) > 0 {
						if state == stateRequestLine {
							return fmt.Errorf("incomplete request - missing CRLF termination")
						}
						if state == stateBody {
							r.body = string(accumulated)
						}
					}
					return nil
				}
				break // Need more data
			}

			// Extract complete line
			line := accumulated[:lineEnd]
			accumulated = accumulated[lineEnd+2:] // Skip CRLF

			// Process line based on current state
			if err := r.processLine(line, &state); err != nil {
				return err
			}

			if state == stateBody {
				// Remaining data is body
				if len(accumulated) > 0 {
					r.body = string(accumulated)
				}
				return nil
			}
		}

		if errors.Is(err, io.EOF) {
			break
		}
	}

	if state == stateRequestLine {
		return fmt.Errorf("incomplete request - no request line found")
	}

	return nil
}

func (r *request) processLine(line []byte, state *int) error {
	switch *state {
	case stateRequestLine:
		if err := r.ParseRequestLine(line); err != nil {
			return err
		}
		*state = stateHeaders
	case stateHeaders:
		if len(line) == 0 {
			// Empty line indicates start of body
			*state = stateBody
		} else {
			r.headers = append(r.headers, string(line))
		}
	}
	return nil
}

// ParseRequestLine parses the request line into method/target/http version
func (r *request) ParseRequestLine(line []byte) error {
	// Trim whitespace and work with bytes
	line = bytes.TrimSpace(line)

	if len(line) == 0 {
		return fmt.Errorf("empty request line")
	}

	// Split on spaces using bytes
	parts := bytes.Split(line, []byte(" "))
	if len(parts) != 3 {
		return fmt.Errorf("invalid request line format, expected 3 parts but got %d", len(parts))
	}

	method := parts[0]
	target := parts[1]
	version := parts[2]

	// Validate method (must be uppercase letters)
	if len(method) == 0 {
		return fmt.Errorf("method cannot be empty")
	}
	for _, b := range method {
		if b < 'A' || b > 'Z' {
			return fmt.Errorf("method must be all uppercase letters")
		}
	}

	// Validate HTTP version using bytes
	httpPrefix := []byte("HTTP/")
	if !bytes.HasPrefix(bytes.ToUpper(version), httpPrefix) {
		return fmt.Errorf("invalid HTTP version format: %s", version)
	}

	versionNum := version[5:] // Skip "HTTP/"
	if !bytes.Equal(versionNum, []byte("1.1")) {
		return fmt.Errorf("only HTTP/1.1 is supported, got: %s", version)
	}

	// Set the parsed values (convert to string only when storing)
	r.requestLine.method = string(method)
	r.requestLine.requestTarget = string(target)
	r.requestLine.httpVersion = string(version)

	return nil
}

func SendRequest(requestString string, serverAddr string) (int, error) {

	if requestString == "" {
		return 0, fmt.Errorf("empty request")
	}

	s := strings.NewReader(requestString)

	if serverAddr == "" {
		return 0, fmt.Errorf("serverAddr not specified")
	}

	b := make([]byte, s.Len())
	s.Read(b)

	// make a new connection to the server
	tcpAddr, err := net.ResolveTCPAddr("tcp", serverAddr)
	if err != nil {
		return 0, fmt.Errorf("could not resolve addr: %v", err)
	}

	conn, err := net.DialTCP("tcp", nil, tcpAddr)
	if err != nil {
		return 0, fmt.Errorf("could not open addr: %v", err)
	}
	defer conn.Close()

	i, err := conn.Write(b)
	if err != nil {
		if errors.Is(err, io.EOF) {
			return i, nil
		}
		return 0, fmt.Errorf("request error: %v", err)
	}
	return i, nil
}
