package request

import (
	"bytes"
	"errors"
	"fmt"
	"io"
	"net"
	"strings"
)

type request struct {
	requestLine requestLine
	headers     []string
	body        string
	state       int
}
type requestLine struct {
	httpVersion   string
	requestTarget string
	method        string
}

const bufferSize = 4096

// parsing states
const (
	stateRequestLine = 0
	stateHeaders     = 1
	stateBody        = 2
)

func (r *request) parse(input io.Reader) error {
	// Reset request
	r.headers = nil
	r.body = ""
	r.requestLine = requestLine{}

	buffer := make([]byte, bufferSize)
	var accumulated []byte
	r.state = stateRequestLine

	for {
		// read into buffer
		n, err := input.Read(buffer)
		if err != nil && !errors.Is(err, io.EOF) {
			return fmt.Errorf("error reading input: %w", err)
		}

		// Append new data to accumulated buffer
		accumulated = append(accumulated, buffer[:n]...)

		// Process complete lines
		for {
			lineEnd := bytes.Index(accumulated, []byte("\r\n"))
			// No crlf terminator found
			if lineEnd == -1 {
				// todo check for incomplete request
				break // Need more data
			}

			line := accumulated[:lineEnd]
			//remove the line and crlf from the accumulated data
			accumulated = accumulated[lineEnd+2:]

			switch r.state {
			case stateRequestLine:
				if err := r.ParseRequestLine(line); err != nil {
					return err
				}
				r.state = stateHeaders
			case stateHeaders:
				if len(line) == 0 {
					// Empty line indicates start of body
					r.state = stateBody
				} else {
					r.headers = append(r.headers, string(line))
				}
			}

		}

		if errors.Is(err, io.EOF) {
			break
		}
	}

	if r.state == stateRequestLine {
		return fmt.Errorf("incomplete request - no request line found")
	}

	return nil
}

// ParseRequestLine parses the request line into method/target/http version
func (r *request) ParseRequestLine(line []byte) error {
	// Trim whitespace and work with bytes
	line = bytes.TrimSpace(line)

	if len(line) == 0 {
		return fmt.Errorf("empty request line")
	}

	// Split on spaces using bytes
	parts := bytes.Split(line, []byte(" "))
	if len(parts) != 3 {
		return fmt.Errorf("invalid request line format, expected 3 parts but got %d", len(parts))
	}

	method := parts[0]
	target := parts[1]
	version := parts[2]

	// Validate method (must be uppercase letters)
	if len(method) == 0 {
		return fmt.Errorf("method cannot be empty")
	}
	for _, b := range method {
		if b < 'A' || b > 'Z' {
			return fmt.Errorf("method must be all uppercase letters")
		}
	}

	// Validate HTTP version using bytes
	httpPrefix := []byte("HTTP/")
	if !bytes.HasPrefix(bytes.ToUpper(version), httpPrefix) {
		return fmt.Errorf("invalid HTTP version format: %s", version)
	}

	versionNum := version[5:] // Skip "HTTP/"
	if !bytes.Equal(versionNum, []byte("1.1")) {
		return fmt.Errorf("only HTTP/1.1 is supported, got: %s", version)
	}

	r.requestLine.method = string(method)
	r.requestLine.requestTarget = string(target)
	r.requestLine.httpVersion = string(version)

	return nil
}

func SendRequest(requestString string, serverAddr string) (int, error) {

	if requestString == "" {
		return 0, fmt.Errorf("empty request")
	}

	s := strings.NewReader(requestString)

	if serverAddr == "" {
		return 0, fmt.Errorf("serverAddr not specified")
	}

	b := make([]byte, s.Len())
	s.Read(b)

	// make a new connection to the server
	tcpAddr, err := net.ResolveTCPAddr("tcp", serverAddr)
	if err != nil {
		return 0, fmt.Errorf("could not resolve addr: %v", err)
	}

	conn, err := net.DialTCP("tcp", nil, tcpAddr)
	if err != nil {
		return 0, fmt.Errorf("could not open addr: %v", err)
	}
	defer conn.Close()

	i, err := conn.Write(b)
	if err != nil {
		if errors.Is(err, io.EOF) {
			return i, nil
		}
		return 0, fmt.Errorf("request error: %v", err)
	}
	return i, nil
}
