package request

import (
	"errors"
	"fmt"
	"io"
	"net"
	"strings"
)

type request struct {
	requestLine requestLine
	headers     []string
	body        string
	state       string // request-line, headers, body, done
}
type requestLine struct {
	httpVersion   string
	requestTarget string
	method        string
}

const bufferSize = 8

func (r *request) parse(input io.Reader) error {
	// Read all input into memory - simpler for HTTP request parsing
	data, err := io.ReadAll(input)
	if err != nil {
		return fmt.Errorf("error reading input: %w", err)
	}

	// Split into lines using CRLF
	content := string(data)
	lines := strings.Split(content, "\r\n")

	if len(lines) == 0 {
		return fmt.Errorf("empty request")
	}

	// Parse request line (first line)
	if err := r.ParseRequestLine([]byte(lines[0])); err != nil {
		return err
	}

	// Parse headers and find body separator
	bodyStartIndex := -1
	for i := 1; i < len(lines); i++ {
		line := lines[i]
		if line == "" {
			// Empty line indicates start of body
			bodyStartIndex = i + 1
			break
		}
		r.headers = append(r.headers, line)
	}

	// Parse body if present
	if bodyStartIndex != -1 && bodyStartIndex < len(lines) {
		r.body = strings.Join(lines[bodyStartIndex:], "\r\n")
	}

	return nil
}

// ParseRequestLine parses the request line into method/target/http version
func (r *request) ParseRequestLine(b []byte) error {
	line := strings.TrimSpace(string(b))

	if line == "" {
		return fmt.Errorf("empty request line")
	}

	parts := strings.Split(line, " ")
	if len(parts) != 3 {
		return fmt.Errorf("invalid request line format, expected 3 parts but got %d", len(parts))
	}

	method, target, version := parts[0], parts[1], parts[2]

	// Validate method (must be uppercase letters)
	if method == "" {
		return fmt.Errorf("method cannot be empty")
	}
	for _, char := range method {
		if char < 'A' || char > 'Z' {
			return fmt.Errorf("method must be all uppercase letters")
		}
	}

	// Validate HTTP version
	if !strings.HasPrefix(strings.ToUpper(version), "HTTP/") {
		return fmt.Errorf("invalid HTTP version format: %s", version)
	}

	versionNumber := strings.TrimPrefix(strings.ToUpper(version), "HTTP/")
	if versionNumber != "1.1" {
		return fmt.Errorf("only HTTP/1.1 is supported, got: %s", version)
	}

	// Set the parsed values
	r.requestLine.method = method
	r.requestLine.requestTarget = target
	r.requestLine.httpVersion = version

	return nil
}

func SendRequest(requestString string, serverAddr string) (int, error) {

	if requestString == "" {
		return 0, fmt.Errorf("empty request")
	}

	s := strings.NewReader(requestString)

	if serverAddr == "" {
		return 0, fmt.Errorf("serverAddr not specified")
	}

	b := make([]byte, s.Len())
	s.Read(b)

	// make a new connection to the server
	tcpAddr, err := net.ResolveTCPAddr("tcp", serverAddr)
	if err != nil {
		return 0, fmt.Errorf("could not resolve addr: %v", err)
	}

	conn, err := net.DialTCP("tcp", nil, tcpAddr)
	if err != nil {
		return 0, fmt.Errorf("could not open addr: %v", err)
	}
	defer conn.Close()

	i, err := conn.Write(b)
	if err != nil {
		if errors.Is(err, io.EOF) {
			return i, nil
		}
		return 0, fmt.Errorf("request error: %v", err)
	}
	return i, nil
}
