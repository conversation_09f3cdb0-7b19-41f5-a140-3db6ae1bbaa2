package request

import (
	"io"
	"strings"
	"testing"

	"github.com/nickabs/http/internal/listener"
)

func TestRequest(t *testing.T) {

	go listener.Listen()

	tests := []struct {
		name        string
		request     string
		serverAddr  string
		wantRequest requestLine
		wantHeaders int
		expectError bool
	}{
		{
			name:    "valid server w. headers and body",
			request: "POST /coffee HTTP/1.1\r\nHost: localhost:42069\r\nUser-Agent: curl/7.81.0\r\nAccept: */*\r\n\r\n{}",
			wantRequest: requestLine{
				httpVersion:   "HTTP/1.1",
				requestTarget: "/coffee",
				method:        "POST",
			},
			wantHeaders: 3,
			serverAddr:  listener.ServerAddr,
			expectError: false,
		},
		{
			name:    "valid server w. headers",
			request: "POST /coffee HTTP/1.1\r\nHost: localhost:42069\r\nUser-Agent: curl/7.81.0\r\nAccept: */*\r\n",
			wantRequest: requestLine{
				httpVersion:   "HTTP/1.1",
				requestTarget: "/coffee",
				method:        "POST",
			},
			wantHeaders: 3,
			serverAddr:  listener.ServerAddr,
			expectError: false,
		},
		{
			name:    "valid server",
			request: "POST /coffee HTTP/1.1\r\n",
			wantRequest: requestLine{
				httpVersion:   "HTTP/1.1",
				requestTarget: "/coffee",
				method:        "POST",
			},
			wantHeaders: 0,
			serverAddr:  listener.ServerAddr,
			expectError: false,
		},

		{
			name:        "bad method",
			request:     "123 /coffee HTTP/1.1\r\n",
			expectError: true,
		},
		{
			name:        "missing method",
			request:     "/coffee HTTP/1.1\r\n",
			expectError: true,
		},
		{
			name:        "bad version ",
			request:     "POST /coffee HTTP/1.2\r\n",
			expectError: true,
		},
		{
			name:        "out of order ",
			request:     "/coffee POST HTTP/1.2\r\n",
			expectError: true,
		},
		{
			name:        "bad name",
			request:     "POST /coffee blah/1.2\r\n",
			expectError: true,
		},
		{
			name:        "empty response",
			request:     "",
			serverAddr:  listener.ServerAddr,
			expectError: true,
		},
		{
			name:        "partial response",
			request:     "POST / HTTP/1.1",
			serverAddr:  listener.ServerAddr,
			expectError: true,
		},
	}

	for i, tt := range tests {

		if i > 60 {
			break
		}
		t.Run(tt.name, func(t *testing.T) {

			r := request{}
			testReader := strings.NewReader((tt.request))

			err := r.parse(testReader)
			if tt.expectError && err != nil {
				t.Log(err)
				return
			}

			if tt.expectError && err == nil {
				t.Errorf("expected an error but got success")
				return
			}

			if !tt.expectError && err != nil {
				t.Errorf("did not expect error but got %v", err)
				return
			}

			if len(r.headers) != tt.wantHeaders {
				t.Errorf("wanted %v headers, got %v\n", tt.wantHeaders, len(r.headers))
			}

			if r.requestLine.httpVersion != tt.wantRequest.httpVersion {
				t.Errorf("expected httpVersion %v got %v ", tt.wantRequest.httpVersion, r.requestLine.httpVersion)
			}
			if r.requestLine.requestTarget != tt.wantRequest.requestTarget {
				t.Errorf("expected requestTarget %v got %v ", tt.wantRequest.requestTarget, r.requestLine.requestTarget)
			}
			if r.requestLine.method != tt.wantRequest.method {
				t.Errorf("expected requestTarget %v got %v ", tt.wantRequest.method, r.requestLine.method)
			}

			/* todo
			_, err = SendRequest(tt.request, tt.serverAddr)

			if !tt.expectError && err != nil {
				t.Errorf("did not expect error but got %v", err)
				return
			}

			if tt.expectError && err == nil {
				t.Error("expected error but got success")
				return
			}
			*/

		})
	}
}

type chunkReader struct {
	data            string
	numBytesPerRead int
	pos             int
}

func TestChunkReaderInput(t *testing.T) {
	t.Run("chunking test", func(t *testing.T) {

		req := request{}

		reader := &chunkReader{
			data:            "GET / HTTP/1.1\r\nHost: localhost:42069\r\nUser-Agent: curl/7.81.0\r\nAccept: */*\r\n\r\n",
			numBytesPerRead: 3,
			pos:             0,
		}

		for i := 1; i <= len(reader.data); i++ {

			t.Logf("attempting reads of %v bytes", i)

			reader.numBytesPerRead = i
			reader.pos = 0
			err := req.parse(reader)
			if err != nil {
				t.Fatalf("could not create request from reader %v", err)
			}

			if req.requestLine.method != "GET" {
				t.Errorf("got %v expected GET", req.requestLine.method)
			}

			if len(req.headers) != 3 {
				t.Errorf("expected 3 headers got %v", len(req.headers))
			}
		}
	})
}

// Read reads up to len(p) or numBytesPerRead bytes from the string per call
// its useful for simulating reading a variable number of bytes per chunk from a network connection
func (cr *chunkReader) Read(p []byte) (n int, err error) {
	if cr.pos >= len(cr.data) {
		return 0, io.EOF
	}
	endIndex := cr.pos + cr.numBytesPerRead
	if endIndex > len(cr.data) {
		endIndex = len(cr.data)
	}
	n = copy(p, cr.data[cr.pos:endIndex])
	cr.pos += n
	if n > cr.numBytesPerRead {
		n = cr.numBytesPerRead
		cr.pos -= n - cr.numBytesPerRead
	}
	return n, nil
}
