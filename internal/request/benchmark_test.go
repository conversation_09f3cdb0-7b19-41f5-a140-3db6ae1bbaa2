package request

import (
	"bytes"
	"io"
	"strings"
	"testing"
)

// Sample HTTP request for benchmarking
const sampleRequest = "GET /api/users HTTP/1.1\r\nHost: example.com\r\nUser-Agent: Go-http-client/1.1\r\nAccept: application/json\r\nContent-Type: application/json\r\nContent-Length: 25\r\n\r\n{\"name\": \"test user\"}"

// Benchmark the current simplified string-based approach
func BenchmarkParseStringBased(b *testing.B) {
	req := &request{}
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		reader := strings.NewReader(sampleRequest)
		err := req.parse(reader)
		if err != nil {
			b.Fatal(err)
		}
	}
}

// Benchmark a hypothetical byte-based approach
func BenchmarkParseByteBasedHypothetical(b *testing.B) {
	req := &request{}
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		reader := strings.NewReader(sampleRequest)
		err := parseBytesBased(req, reader)
		if err != nil {
			b.Fatal(err)
		}
	}
}

// Simplified byte-based parser for comparison
func parseBytesBased(r *request, input io.Reader) error {
	// Reset the request struct
	r.headers = nil
	r.body = ""
	r.requestLine = requestLine{}

	data, err := io.ReadAll(input)
	if err != nil {
		return err
	}

	if len(data) == 0 {
		return io.EOF
	}

	// Find CRLF positions using bytes
	crlf := []byte("\r\n")
	if !bytes.Contains(data, crlf) {
		return io.EOF
	}

	// Split on CRLF using bytes
	lines := bytes.Split(data, crlf)
	
	// Parse request line
	if len(lines) == 0 {
		return io.EOF
	}
	
	if err := parseRequestLineBytes(r, lines[0]); err != nil {
		return err
	}

	// Parse headers
	bodyStartIndex := -1
	for i := 1; i < len(lines); i++ {
		if len(lines[i]) == 0 {
			bodyStartIndex = i + 1
			break
		}
		r.headers = append(r.headers, string(lines[i]))
	}

	// Parse body
	if bodyStartIndex != -1 && bodyStartIndex < len(lines) {
		bodyParts := lines[bodyStartIndex:]
		r.body = string(bytes.Join(bodyParts, crlf))
	}

	return nil
}

func parseRequestLineBytes(r *request, line []byte) error {
	// Trim whitespace
	line = bytes.TrimSpace(line)
	
	if len(line) == 0 {
		return io.EOF
	}

	parts := bytes.Split(line, []byte(" "))
	if len(parts) != 3 {
		return io.EOF
	}

	method := string(parts[0])
	target := string(parts[1])
	version := string(parts[2])

	// Basic validation (simplified for benchmark)
	if len(method) == 0 {
		return io.EOF
	}

	r.requestLine.method = method
	r.requestLine.requestTarget = target
	r.requestLine.httpVersion = version

	return nil
}

// Benchmark memory allocations
func BenchmarkParseStringBasedAllocs(b *testing.B) {
	req := &request{}
	
	b.ReportAllocs()
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		reader := strings.NewReader(sampleRequest)
		err := req.parse(reader)
		if err != nil {
			b.Fatal(err)
		}
	}
}

func BenchmarkParseByteBasedAllocs(b *testing.B) {
	req := &request{}
	
	b.ReportAllocs()
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		reader := strings.NewReader(sampleRequest)
		err := parseBytesBased(req, reader)
		if err != nil {
			b.Fatal(err)
		}
	}
}
