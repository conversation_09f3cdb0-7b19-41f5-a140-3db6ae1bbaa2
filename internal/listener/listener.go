package listener

import (
	"errors"
	"fmt"
	"io"
	"log"
	"net"
	"strings"
)

const bufferSize = 8
const ServerAddr = "localhost:42069"

// read io into a buffer and add each \n terminated line to the returned chanel
func readInput(input io.ReadCloser) <-chan string {

	c := make(chan string)
	go func() {
		defer close(c)

		buffer := make([]byte, bufferSize)
		var currentLine strings.Builder

		for {
			n, err := input.Read(buffer)

			if err != nil {
				if errors.Is(err, io.EOF) {
					if currentLine.Len() > 0 {
						c <- currentLine.String()
					}
					break
				}
				panic(fmt.Sprintf("error reading file: %v bytes read, error: %v", n, err))
			}

			chunk := string(buffer[:n])
			parts := strings.Split(chunk, "\n")

			for i, part := range parts {
				if i > 0 {
					// print and reset on finding a newline
					c <- currentLine.String()
					currentLine.Reset()
				}
				currentLine.WriteString(part)
			}
		}

	}()
	return c
}

// create a tcp listener
// accept connections and print receieved data one line at a time
func Listen() {
	listener, err := net.Listen("tcp", ServerAddr)
	if err != nil {
		panic(fmt.Sprintf("could not create listener %v", err))
	}
	fmt.Printf("listening on %s \n", ServerAddr)

	defer listener.Close()

	for {
		conn, err := listener.Accept()
		if err != nil {
			log.Fatalf("could not get a connection %v", err)
			return
		}
		fmt.Printf("connection accepted from %s \n", conn.RemoteAddr().String())
		defer conn.Close()

		c := readInput(conn)
		for line := range c {
			fmt.Println(line)
		}
	}

}
